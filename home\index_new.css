/* 页面基础样式 */
.page {
	width: 100%;
	min-height: 100vh;
	background-color: #ffffff;
}

.main-content {
	max-width: 1920px;
	margin: 0 auto;
	display: flex;
	flex-direction: column;
}

/* 导航样式已移至 ../component/navigation/navigation.css */

/* 轮播图样式已移至 ../component/carousel/carousel.css */

/* 产品预览区域 */
.product-preview {
	background: linear-gradient(271deg, #e6b2b2 0%, #ffe6e6 100%);
	background-size: cover;
	background-position: center;
	height: 680px;
	margin-top: 60px;
	padding: 57px 0;
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.product-preview-container {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	max-width: 1250px;
	position: relative;
}

.product-preview-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 32px;
	flex: 1;
	margin: 0 156px;
}

.nav-arrow {
	background: none;
	border: none;
	cursor: pointer;
	padding: 10px;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: opacity 0.3s ease;
}

.nav-arrow:hover {
	opacity: 0.7;
}

.nav-arrow img {
	width: 40px;
	height: 40px;
	object-fit: contain;
}

.nav-arrow-left {
	margin-right: 20px;
}

.nav-arrow-right {
	margin-left: 20px;
}

.section-header {
	display: flex;
	justify-content: center;
}

.section-title {
	font-size: 30px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	line-height: 42px;
	margin: 0;
}

.product-categories {
	display: flex;
	gap: 80px;
	justify-content: center;
}

.category-link {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	text-decoration: none;
	line-height: 20px;
	transition: color 0.3s ease;
}

.category-link:hover {
	color: #0055c3;
}

.product-gallery {
	width: 100%;
	display: flex;
	align-items: center;
	gap: 38px;
	justify-content: flex-end;
}

.main-product-image {
	width: 600px;
	height: 300px;
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.main-product-image img {
	max-width: 100%;
	max-height: 100%;
	object-fit: contain;
}

.product-thumbnails {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.product-image-item {
	width: 105px;
	height: 102px;
}

.product-thumbnail {
	width: 100%;
	height: 100%;
	border-radius: 8px;
	box-shadow: 0px 0px 6px 0px rgba(113, 113, 113, 0.3);
	object-fit: cover;
	cursor: pointer;
	transition: transform 0.3s ease;
}

.product-thumbnail:hover {
	transform: scale(1.05);
}

/* 活跃状态样式 */
.category-link.active {
	color: #0055c3;
	font-weight: bold;
	text-decoration: underline;
}

.product-thumbnail.active {
	border: 2px solid #0055c3;
	transform: scale(1.05);
	box-shadow: 0px 0px 8px 0px rgba(0, 85, 195, 0.4);
}

.config-tab.active {
	background-color: #0055c3;
	color: #ffffff;
}

/* 过渡动画 */
.main-product-image img {
	transition: opacity 0.3s ease;
}

.product-thumbnail {
	transition: all 0.3s ease;
}

.category-link {
	transition: all 0.3s ease;
}

.config-tab {
	transition: all 0.3s ease;
}

/* 产品切换动画效果 */
.product-preview-content {
	transition: opacity 0.3s ease;
}

.product-preview-content.switching {
	opacity: 0.7;
}

/* 左右箭头悬停效果增强 */
.nav-arrow {
	transition: all 0.3s ease;
}

.nav-arrow:hover {
	opacity: 0.7;
	transform: scale(1.1);
}

.nav-arrow:active {
	transform: scale(0.95);
}

.product-hover-demo {
	display: flex;
	align-items: flex-end;
	gap: 887px;
	width: 100%;
	max-width: 1061px;
}

.hover-label {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #ff1d1d;
	line-height: 20px;
}

.product-config {
	width: 100%;
	display: flex;
	align-items: center;
	gap: 16px;
	margin-top: 60px;
	padding: 0 20px;
	justify-content: space-between;
}

.config-tabs {
	display: flex;
	/* flex-direction: column; */
	gap: 40px;
}

.config-tab {
	background-color: #000000;
	color: #ffffff;
	font-family: AlibabaPuHuiTi-Regular;
	line-height: 20px;
	border: none;
	padding: 7px 18px;
	height: 34px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.config-tab:not(.active) {
	background-color: transparent;
	color: #000000;
	border: 1px solid #000000;
}

.config-tab:hover {
	background-color: #333333;
	color: #fff;
}

.product-detail-image {
	width: 71px;
	height: 53px;
	margin-top: 25px;
}

.product-specs {
	display: flex;
	align-items: center;
	gap: 30px;
}

.spec-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 9px;
}

.spec-value {
	font-size: 18px;
	font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
	color: #000000;
	line-height: 21px;
}

.spec-label {
	font-size: 12px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	color: #000000;
	line-height: 17px;
}

.spec-divider {
	width: 1px;
	height: 47px;
	border-left: 1px solid #000000;
}

/* 公司介绍区域 */
.company-intro {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 15px;
	padding-top: 57px;
	text-align: center;
}

.company-title {
	font-size: 36px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	line-height: 50px;
	font-weight: 400;
	margin: 0;
}

.company-description {
	font-size: 20px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	line-height: 27px;
	margin: 0;
	text-align: right;
	max-width: 910px;
}

/* 解决方案区域 */
.solutions-section {
	background-color: #000000;
	height: 120px;
	margin: 56px auto 0;
	display: flex;
	align-items: center;
	justify-content: center;
}

.solutions-content {
	display: flex;
	width: 1030px;
	/* align-items: center; */
}

.solutions-title {
	text-align: center;
	flex: 1;
	font-size: 24px;
	font-family: PingFangSC-Medium;
	font-weight: 500;
	color: #ffffff;
	line-height: 120px;
	margin: 0;
}

.solutions-btn {
	text-align: center;
	flex: 1;
	background-image: url(./img/11c09b6d4d974cfdb0174ba7f2c9e083_mergeImage.png);
	background-size: cover;
	color: #ffffff;
	font-size: 24px;
	font-family: PingFangSC-Medium;
	font-weight: 500;
	border: none;
	line-height: 120px;
	cursor: pointer;
	transition: opacity 0.3s ease;
}

.solutions-btn:hover {
	opacity: 0.8;
}

/* 新闻区域 */
.news-section {
	padding: 60px 0;
	max-width: 1030px;
	margin: 0 auto;
}

.news-header {
	display: flex;
	align-items: end;
	justify-content: space-between;
	margin-bottom: 40px;
	padding-left: 77px;
}

.news-title {
	font-size: 36px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	line-height: 50px;
	margin: 0;
	font-weight: 500;
}

.news-nav {
	display: flex;
	align-items: center;
	gap: 10px;
}

.browse-all-link {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #767676;
	text-decoration: none;
	line-height: 20px;
	transition: color 0.3s ease;
}

.browse-all-link.active {
	color: #003a85;
}

.browse-all-link:hover {
	color: #0055c3;
}

.nav-arrow {
	width: 14px;
	height: 14px;
}

.news-divider {
	width: 77px;
	height: 1px;
	margin: 0 20px;
}

.news-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 20px;
	max-width: 1160px;
	margin: 0 auto;
}

.news-card {
	background-color: #ffffff;
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	transition: transform 0.3s ease, box-shadow 0.3s ease;
	display: flex;
	flex-direction: column;
	height: 380px;
}

.news-card:hover {
	transform: translateY(-4px);
	box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.news-image {
	width: 100%;
	height: 180px;
	object-fit: cover;
	object-position: center;
	flex-shrink: 0;
	display: block;
}

.news-content {
	padding: 20px;
	display: flex;
	flex-direction: column;
	gap: 10px;
	flex: 1;
	background-color: #f4f4f4;
}

.news-card-title {
	font-size: 18px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	line-height: 24px;
	margin: 0;
	font-weight: 500;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.news-card-summary {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Light;
	font-weight: 300;
	color: #666666;
	line-height: 20px;
	margin: 0;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1200px) {
	.news-grid {
		grid-template-columns: repeat(3, 1fr);
		max-width: 900px;
	}
}

@media (max-width: 900px) {
	.news-grid {
		grid-template-columns: repeat(2, 1fr);
		max-width: 600px;
	}

	.news-header {
		flex-direction: column;
		gap: 20px;
		text-align: center;
	}
}

@media (max-width: 600px) {
	.news-grid {
		grid-template-columns: 1fr;
		max-width: 400px;
	}

	.news-section {
		padding: 40px 0;
	}

	.news-title {
		font-size: 28px;
	}
}

/* 联系我们样式已移至 ../component/contact/contact.css */

/* 页脚样式已移至 ../component/footer/footer.css */
